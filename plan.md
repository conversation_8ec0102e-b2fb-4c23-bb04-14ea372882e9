# User Stories

As a reader I want to be able to:

- Browse and search for books,Author
- Read a book
- Read a book chapter
- Read a book chapter and save it to my library
- Contact with admin
- Comment on a book chapter
- Rate a book

As a admin I want to:

- CRUD books
- CRUD book chapters
- User analytics
- User notifications


# Task List

- [] Create a database
- [] Interface design
- [] Create a dashboard
- [] API design
- [] create a login page
- [] create a bookview page
- [] create a bookchapterview page


| Task                          | Level  | Appetite (timebox) | Dependencies  | Risk | “Done” looks like                                                                                |
| ----------------------------- | ------ | -----------------: | ------------- | ---- | ------------------------------------------------------------------------------------------------ |
| Create a database             | **L1** |          0.5–1 day | —             | Low  | Core tables exist; local migrations run; seed data for 2 books/5 chapters                        |
| API design                    | **L1** |            0.5 day | DB            | Low  | Endpoints sketched & stubbed: `GET /books`, `GET /books/:slug`, `GET /books/:slug/chapters/:idx` |
| create a bookview page        | **L1** |              1 day | DB, API       | Low  | Book details + list of chapters; open first chapter                                              |
| create a bookchapterview page | **L1** |              1 day | DB, API       | Med  | Mobile-friendly reader; next/prev; saves last position (localStorage)                            |
| create a login page           | **L2** |          0.5–1 day | DB, API       | Med  | Email+password or OAuth; session persists; protected route shows user name                       |
| Interface design              | **L2** |          0.5–1 day | —             | Low  | Base layout, typography, light/dark; reusable components (Button, Card, Reader)                  |
| Create a dashboard (admin)    | **L3** |         1.5–2 days | DB, API, Auth | Med  | Admin can CRUD books/chapters from UI; guarded by role                                           |



# Project structure
```
src/
  app/
    (public)/
      layout.tsx
      page.tsx                       # homepage / discover
      books/
        page.tsx                     # browse/search
        [slug]/
          page.tsx                   # book view (synopsis + chapters)
          chapters/
            [idx]/page.tsx           # chapter reader
      me/
        library/page.tsx             # user library
    (admin)/
      layout.tsx                     # admin shell (guarded)
      dashboard/page.tsx
      books/
        new/page.tsx
        [id]/
          page.tsx                   # edit book
          chapters/new/page.tsx
    api/                              # public JSON endpoints (for future apps/integrations)
      books/route.ts                  # GET /api/books
      books/[slug]/route.ts           # GET /api/books/:slug
      books/[slug]/chapters/[idx]/route.ts
      comments/route.ts               # GET (list); POST (create) if you want REST style
      ratings/route.ts
      contact/route.ts                # POST support message
      revalidate/route.ts             # (optional) webhook to purge caches
    auth/
      callback/page.tsx               # (if custom OAuth UI)
  features/                           # domain modules (UI + server logic co-located)
    auth/
      server.ts                       # getSession(), requireUser(), RBAC helpers
      schemas.ts                      # zod for credentials/register
    books/
      components/                     # BookCard, ChapterList, etc.
      repo.ts                         # Prisma queries (server-only)
      actions.ts                      # server actions (create/update book, etc.)
      schemas.ts
      queries.ts                      # wrappers you can import in Server Components
    chapters/
      components/
      repo.ts
      actions.ts                      # publish/schedule, comment create, etc.
      schemas.ts
    comments/
      components/CommentList.tsx
      actions.ts
      repo.ts
      schemas.ts
    ratings/
      actions.ts
      repo.ts
      schemas.ts
    notifications/
      actions.ts                      # subscribe/unsubscribe
      service.ts                      # email/in-app senders
    analytics/
      events.ts                       # capture helpers
      repo.ts                         # aggregates for dashboard
      components/Charts.tsx
  components/ui/                      # design system bits (Button, Input, Card, Modal, Reader)
  lib/
    prisma.ts
    env.ts                            # zod-validated env
    cache.ts                          # revalidateTag helpers
    rbac.ts                           # role checks
    email.ts                          # Postmark/SES wrapper
    rate-limit.ts                     # simple limiter (IP/user)
    sanitize.ts                       # DOMPurify/bleach server-side
  prisma/
    schema.prisma
    seed.ts
  styles/
    globals.css
    prose.css                         # typography for chapters
  middleware.ts                       # protect /(admin) and optionally /me
tests/
  e2e/                                # Playwright/Cypress
  unit/                               # vitest/jest
