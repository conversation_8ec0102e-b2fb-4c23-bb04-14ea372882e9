datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  email     String   @unique
  password  String
  name      String?
  role      String   @default("user")
}

model Book {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  title     String
  slug      String   @unique
  synopsis  String
  authorId  String
  author    Author   @relation(fields: [authorId], references: [id])
  chapters  Chapter[]
  ratings   Rating[]
}

model Chapter {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  text      String
  title     String
  slug      String   @unique
  synopsis  String
  bookId    String
  book      Book     @relation(fields: [bookId], references: [id])

}

model Comment {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  text      String
}

model Author {}

model Rating {}

model History {}